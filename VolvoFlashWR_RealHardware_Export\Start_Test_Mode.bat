@echo off
echo ========================================
echo VolvoFlashWR - Test Mode (No Hardware)
echo ========================================
echo.
echo This mode runs without real hardware for testing.
echo.
echo Press any key to continue...
pause >nul

REM Set environment for test mode
set USE_DUMMY_IMPLEMENTATIONS=true
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug
set PHOENIX_VOCOM_ENABLED=false

echo Starting VolvoFlashWR in Test Mode...

REM Change to Application directory
echo Changing to Application directory...
cd /d "Application"
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Application directory not found!
    pause
    exit /b 1
)

"VolvoFlashWR.Launcher.exe" --mode=Dummy

echo Application has exited.
pause
