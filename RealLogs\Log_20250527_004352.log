Log started at 5/27/2025 12:43:52 AM
2025-05-27 00:43:52.846 [Information] LoggingService: Logging service initialized
2025-05-27 00:43:52.858 [Information] App: Verbose logging enabled
2025-05-27 00:43:52.860 [Information] AppConfigurationService: Initializing configuration service
2025-05-27 00:43:52.860 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export\VolvoFlashWR_RealHardware_Export\Application\Config
2025-05-27 00:43:52.923 [Information] AppConfigurationService: Configuration loaded from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export\VolvoFlashWR_RealHardware_Export\Application\Config\app_config.json
2025-05-27 00:43:52.924 [Information] AppConfigurationService: Configuration service initialized successfully
2025-05-27 00:43:52.925 [Information] App: Configuration service initialized successfully
2025-05-27 00:43:52.926 [Information] App: Using dummy implementations for all services
2025-05-27 00:43:52.926 [Information] App: Creating dummy Vocom service
2025-05-27 00:43:52.928 [Information] DummyVocomService: Initializing dummy Vocom service
2025-05-27 00:43:52.928 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-05-27 00:43:52.928 [Information] App: Dummy Vocom service initialized successfully
2025-05-27 00:43:52.928 [Information] App: Creating dummy ECU communication service
2025-05-27 00:43:52.929 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-05-27 00:43:52.930 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-05-27 00:43:53.433 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-05-27 00:43:53.433 [Information] App: Dummy ECU communication service initialized successfully
2025-05-27 00:43:53.434 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-05-27 00:43:53.434 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-05-27 00:43:53.444 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-05-27 00:43:53.446 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-05-27 00:43:53.455 [Information] BackupService: Initializing backup service
2025-05-27 00:43:53.455 [Information] BackupService: Backup service initialized successfully
2025-05-27 00:43:53.456 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-05-27 00:43:53.456 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-05-27 00:43:53.457 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-05-27 00:43:53.491 [Information] BackupService: Compressing backup data
2025-05-27 00:43:53.500 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-05-27 00:43:53.501 [Information] BackupServiceFactory: Created template for category: Production
2025-05-27 00:43:53.501 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-05-27 00:43:53.502 [Information] BackupService: Compressing backup data
2025-05-27 00:43:53.503 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (449 bytes)
2025-05-27 00:43:53.503 [Information] BackupServiceFactory: Created template for category: Development
2025-05-27 00:43:53.503 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-05-27 00:43:53.504 [Information] BackupService: Compressing backup data
2025-05-27 00:43:53.505 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (444 bytes)
2025-05-27 00:43:53.505 [Information] BackupServiceFactory: Created template for category: Testing
2025-05-27 00:43:53.505 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-05-27 00:43:53.505 [Information] BackupService: Compressing backup data
2025-05-27 00:43:53.506 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (447 bytes)
2025-05-27 00:43:53.506 [Information] BackupServiceFactory: Created template for category: Archived
2025-05-27 00:43:53.507 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-05-27 00:43:53.507 [Information] BackupService: Compressing backup data
2025-05-27 00:43:53.508 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-05-27 00:43:53.508 [Information] BackupServiceFactory: Created template for category: Critical
2025-05-27 00:43:53.508 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-05-27 00:43:53.509 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-05-27 00:43:53.509 [Information] BackupService: Compressing backup data
2025-05-27 00:43:53.510 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-05-27 00:43:53.510 [Information] BackupServiceFactory: Created template with predefined tags
2025-05-27 00:43:53.510 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-05-27 00:43:53.511 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-05-27 00:43:53.514 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-05-27 00:43:53.519 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-05-27 00:43:53.575 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export\VolvoFlashWR_RealHardware_Export\Application\Schedules\backup_schedules.json
2025-05-27 00:43:53.576 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-05-27 00:43:53.577 [Information] BackupSchedulerService: Starting backup scheduler
2025-05-27 00:43:53.577 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-05-27 00:43:53.577 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-05-27 00:43:53.578 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-05-27 00:43:53.579 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-05-27 00:43:53.583 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-05-27 00:43:53.583 [Information] App: Flash operation monitor service initialized successfully
2025-05-27 00:43:53.594 [Information] LicensingService: Initializing licensing service
2025-05-27 00:43:53.651 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-05-27 00:43:53.653 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-05-27 00:43:53.654 [Information] App: Licensing service initialized successfully
2025-05-27 00:43:53.654 [Information] App: License status: Trial
2025-05-27 00:43:53.654 [Information] App: Trial period: 30 days remaining
2025-05-27 00:43:53.655 [Information] BackupSchedulerService: Getting all backup schedules
2025-05-27 00:43:53.688 [Information] DummyVocomService: Initializing dummy Vocom service
2025-05-27 00:43:53.688 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-05-27 00:43:53.688 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-05-27 00:43:54.188 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-05-27 00:43:54.189 [Information] BackupService: Initializing backup service
2025-05-27 00:43:54.189 [Information] BackupService: Backup service initialized successfully
2025-05-27 00:43:54.189 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-05-27 00:43:54.189 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-05-27 00:43:54.190 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export\VolvoFlashWR_RealHardware_Export\Application\Schedules\backup_schedules.json
2025-05-27 00:43:54.191 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-05-27 00:43:54.192 [Information] BackupService: Getting predefined backup categories
2025-05-27 00:43:54.193 [Information] MainViewModel: Services initialized successfully
2025-05-27 00:43:54.199 [Information] MainViewModel: Scanning for Vocom devices
2025-05-27 00:43:54.200 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-05-27 00:43:54.313 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-05-27 00:43:54.315 [Information] MainViewModel: Found 1 Vocom device(s)
2025-05-27 00:44:28.366 [Information] MainViewModel: Scanning for Vocom devices
2025-05-27 00:44:28.368 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-05-27 00:44:28.482 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-05-27 00:44:28.483 [Information] MainViewModel: Found 1 Vocom device(s)
2025-05-27 00:45:15.176 [Information] MainViewModel: Scanning for Vocom devices
2025-05-27 00:45:15.178 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-05-27 00:45:15.286 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-05-27 00:45:15.286 [Information] MainViewModel: Found 1 Vocom device(s)
