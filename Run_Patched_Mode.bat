@echo on
echo Starting VolvoFlashWR in Patched Mode...

REM Run the driver copy script first
call copy_vocom_driver.bat

REM Set environment variables
set USE_PATCHED_IMPLEMENTATION=true
set VERBOSE_LOGGING=true
set PHOENIX_VOCOM_ENABLED=false
set USE_DUMMY_IMPLEMENTATIONS=false

echo Environment variables set:
echo USE_PATCHED_IMPLEMENTATION=%USE_PATCHED_IMPLEMENTATION%
echo VERBOSE_LOGGING=%VERBOSE_LOGGING%
echo PHOENIX_VOCOM_ENABLED=%PHOENIX_VOCOM_ENABLED%
echo USE_DUMMY_IMPLEMENTATIONS=%USE_DUMMY_IMPLEMENTATIONS%

REM Check if the launcher exists
if exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" (
    echo Starting launcher...
    start "" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe"
) else (
    echo Launcher not found. Building solution...

    REM Build the solution
    dotnet build -c Debug

    REM Check if build was successful
    if %ERRORLEVEL% NEQ 0 (
        echo Build failed. Please check the error messages.
        pause
        exit /b 1
    )

    REM Check if the launcher exists after building
    if exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" (
        echo Starting launcher...
        start "" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe"
    ) else (
        echo Launcher not found after building. Please check the build configuration.
        pause
        exit /b 1
    )
)

echo VolvoFlashWR started in Patched Mode.
exit /b 0
