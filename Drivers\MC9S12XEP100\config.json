{"microcontroller": "MC9S12XEP100", "flash_size": 768000, "eeprom_size": 4096, "ram_size": 49152, "sector_size": 1024, "phrase_size": 8, "d_flash_size": 32768, "buffer_ram_size": 2048, "max_clock_frequency": 50000000, "protocols": {"CAN": {"enabled": true, "registers": {"CAN0_CTL0": "0x0140", "CAN0_CTL1": "0x0141", "CAN0_BTR0": "0x0142", "CAN0_BTR1": "0x0143"}, "baud_rates": {"high_speed": 500000, "low_speed": 125000}}, "SPI": {"enabled": true, "registers": {"SPI0_CR1": "0x00D0", "SPI0_CR2": "0x00D1", "SPI0_BR": "0x00D2", "SPI0_SR": "0x00D3", "SPI0_DR": "0x00D5"}, "clock_rates": {"high_speed": 12500000, "low_speed": 1000000}}, "SCI": {"enabled": true, "registers": {"SCI0_BDH": "0x00C0", "SCI0_BDL": "0x00C1", "SCI0_CR1": "0x00C2", "SCI0_CR2": "0x00C3", "SCI0_SR1": "0x00C4", "SCI0_SR2": "0x00C5", "SCI0_DRH": "0x00C6", "SCI0_DRL": "0x00C7"}, "baud_rates": {"high_speed": 115200, "low_speed": 9600}}, "IIC": {"enabled": true, "registers": {"IBAD": "0x0036", "IBFD": "0x0037", "IBCR": "0x0038", "IBSR": "0x0039", "IBDR": "0x003A"}, "clock_rates": {"standard_mode": 100000, "fast_mode": 400000}}}}