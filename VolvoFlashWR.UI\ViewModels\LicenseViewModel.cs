using System;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows.Input;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.UI.Commands;

namespace VolvoFlashWR.UI.ViewModels
{
    /// <summary>
    /// View model for the license view
    /// </summary>
    public class LicenseViewModel : INotifyPropertyChanged
    {
        #region Private Fields

        private readonly ILoggingService _loggingService;
        private readonly ILicensingService _licensingService;
        private bool _isBusy;
        private string _statusMessage;
        private string _statusMessageColor;
        private string _activationKey;
        private LicenseInfo _licenseInfo;
        private bool _isActivationEnabled;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets whether the view model is busy
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            set
            {
                if (_isBusy != value)
                {
                    _isBusy = value;
                    OnPropertyChanged(nameof(IsBusy));
                    OnPropertyChanged(nameof(IsNotBusy));
                }
            }
        }

        /// <summary>
        /// Gets whether the view model is not busy
        /// </summary>
        public bool IsNotBusy => !_isBusy;

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                if (_statusMessage != value)
                {
                    _statusMessage = value;
                    OnPropertyChanged(nameof(StatusMessage));
                }
            }
        }

        /// <summary>
        /// Gets or sets the status message color
        /// </summary>
        public string StatusMessageColor
        {
            get => _statusMessageColor;
            set
            {
                if (_statusMessageColor != value)
                {
                    _statusMessageColor = value;
                    OnPropertyChanged(nameof(StatusMessageColor));
                }
            }
        }

        /// <summary>
        /// Gets or sets the activation key
        /// </summary>
        public string ActivationKey
        {
            get => _activationKey;
            set
            {
                if (_activationKey != value)
                {
                    _activationKey = value;
                    OnPropertyChanged(nameof(ActivationKey));
                    IsActivationEnabled = !string.IsNullOrWhiteSpace(value);
                }
            }
        }

        /// <summary>
        /// Gets or sets the license information
        /// </summary>
        public LicenseInfo LicenseInfo
        {
            get => _licenseInfo;
            set
            {
                if (_licenseInfo != value)
                {
                    _licenseInfo = value;
                    OnPropertyChanged(nameof(LicenseInfo));
                    OnPropertyChanged(nameof(LicenseStatusText));
                    OnPropertyChanged(nameof(IsLicensed));
                    OnPropertyChanged(nameof(IsInTrial));
                    OnPropertyChanged(nameof(IsTrialExpired));
                    OnPropertyChanged(nameof(IsLicenseExpired));
                    OnPropertyChanged(nameof(CanDeactivate));
                }
            }
        }

        /// <summary>
        /// Gets or sets whether activation is enabled
        /// </summary>
        public bool IsActivationEnabled
        {
            get => _isActivationEnabled;
            set
            {
                if (_isActivationEnabled != value)
                {
                    _isActivationEnabled = value;
                    OnPropertyChanged(nameof(IsActivationEnabled));
                }
            }
        }

        /// <summary>
        /// Gets whether the application is licensed
        /// </summary>
        public bool IsLicensed => _licenseInfo?.Status == LicenseStatus.Licensed;

        /// <summary>
        /// Gets whether the application is in trial period
        /// </summary>
        public bool IsInTrial => _licenseInfo?.Status == LicenseStatus.Trial;

        /// <summary>
        /// Gets whether the trial period has expired
        /// </summary>
        public bool IsTrialExpired => _licenseInfo?.Status == LicenseStatus.TrialExpired;

        /// <summary>
        /// Gets whether the license has expired
        /// </summary>
        public bool IsLicenseExpired => _licenseInfo?.Status == LicenseStatus.LicenseExpired;

        /// <summary>
        /// Gets whether the license can be deactivated
        /// </summary>
        public bool CanDeactivate => IsLicensed || IsLicenseExpired;

        /// <summary>
        /// Gets the license status text
        /// </summary>
        public string LicenseStatusText
        {
            get
            {
                if (_licenseInfo == null)
                {
                    return "Unknown";
                }

                switch (_licenseInfo.Status)
                {
                    case LicenseStatus.Unlicensed:
                        return "Not Licensed";
                    case LicenseStatus.Trial:
                        return $"Trial Period ({_licenseInfo.TrialDaysRemaining} days remaining)";
                    case LicenseStatus.Licensed:
                        return $"Licensed (Expires: {_licenseInfo.ExpirationDate?.ToString("yyyy-MM-dd")})";
                    case LicenseStatus.TrialExpired:
                        return "Trial Period Expired";
                    case LicenseStatus.LicenseExpired:
                        return $"License Expired (Expired: {_licenseInfo.ExpirationDate?.ToString("yyyy-MM-dd")})";
                    default:
                        return "Unknown";
                }
            }
        }

        #endregion

        #region Commands

        /// <summary>
        /// Gets the command to activate the license
        /// </summary>
        public ICommand ActivateCommand { get; private set; }

        /// <summary>
        /// Gets the command to deactivate the license
        /// </summary>
        public ICommand DeactivateCommand { get; private set; }

        /// <summary>
        /// Gets the command to close the dialog
        /// </summary>
        public ICommand CloseCommand { get; private set; }

        #endregion

        #region Events

        /// <summary>
        /// Event triggered when the dialog should be closed
        /// </summary>
        public event EventHandler CloseRequested;

        /// <summary>
        /// Event for property changed notifications
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the LicenseViewModel class
        /// </summary>
        /// <param name="loggingService">The logging service</param>
        /// <param name="licensingService">The licensing service</param>
        public LicenseViewModel(ILoggingService loggingService, ILicensingService licensingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _licensingService = licensingService ?? throw new ArgumentNullException(nameof(licensingService));

            // Initialize properties
            _statusMessage = string.Empty;
            _statusMessageColor = "Black";
            _activationKey = string.Empty;
            _isActivationEnabled = false;
            _licenseInfo = _licensingService.GetLicenseInfo();

            // Initialize commands
            ActivateCommand = new AsyncRelayCommand(ActivateAsync, () => IsActivationEnabled && !IsBusy);
            DeactivateCommand = new AsyncRelayCommand(DeactivateAsync, () => CanDeactivate && !IsBusy);
            CloseCommand = new RelayCommand(param => Close());

            // Subscribe to license status changes
            _licensingService.LicenseStatusChanged += (sender, info) => LicenseInfo = info;
        }

        #endregion

        #region Command Methods

        /// <summary>
        /// Activates the license with the provided key
        /// </summary>
        private async Task ActivateAsync()
        {
            try
            {
                IsBusy = true;
                StatusMessage = "Activating license...";
                StatusMessageColor = "Black";

                _loggingService.LogInformation("Attempting to activate license", "LicenseViewModel");

                // Validate input
                if (string.IsNullOrWhiteSpace(ActivationKey))
                {
                    StatusMessage = "Please enter an activation key.";
                    StatusMessageColor = "Red";
                    return;
                }

                // Attempt to activate
                var result = await _licensingService.ActivateWithKeyAsync(ActivationKey);

                if (result.Success)
                {
                    LicenseInfo = result.LicenseInfo;
                    StatusMessage = "License activated successfully.";
                    StatusMessageColor = "Green";
                    ActivationKey = string.Empty;
                    _loggingService.LogInformation("License activated successfully", "LicenseViewModel");
                }
                else
                {
                    StatusMessage = $"Activation failed: {result.ErrorMessage}";
                    StatusMessageColor = "Red";
                    _loggingService.LogWarning($"License activation failed: {result.ErrorMessage}", "LicenseViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusMessageColor = "Red";
                _loggingService.LogError("Error activating license", "LicenseViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// Deactivates the current license
        /// </summary>
        private async Task DeactivateAsync()
        {
            try
            {
                IsBusy = true;
                StatusMessage = "Deactivating license...";
                StatusMessageColor = "Black";

                _loggingService.LogInformation("Attempting to deactivate license", "LicenseViewModel");

                // Attempt to deactivate
                bool result = await _licensingService.DeactivateAsync();

                if (result)
                {
                    LicenseInfo = _licensingService.GetLicenseInfo();
                    StatusMessage = "License deactivated successfully.";
                    StatusMessageColor = "Green";
                    _loggingService.LogInformation("License deactivated successfully", "LicenseViewModel");
                }
                else
                {
                    StatusMessage = "Failed to deactivate license.";
                    StatusMessageColor = "Red";
                    _loggingService.LogWarning("License deactivation failed", "LicenseViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusMessageColor = "Red";
                _loggingService.LogError("Error deactivating license", "LicenseViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// Closes the dialog
        /// </summary>
        private void Close()
        {
            CloseRequested?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">The name of the property that changed</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
