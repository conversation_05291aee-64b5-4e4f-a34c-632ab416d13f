@echo off
echo ========================================
echo Testing Library Verification
echo ========================================
echo.

REM Change to Application directory
echo Changing to Application directory...
cd /d "Application"
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Application directory not found!
    pause
    exit /b 1
)

echo Current directory: %CD%
echo.

REM Verify critical libraries
echo Verifying critical libraries...

if exist "WUDFPuma.dll" (
    echo [OK] WUDFPuma.dll found
) else (
    echo [ERROR] WUDFPuma.dll not found!
)

if exist "Volvo.ApciPlus.dll" (
    echo [OK] Volvo.ApciPlus.dll found
) else (
    echo [ERROR] Volvo.ApciPlus.dll not found!
)

if exist "apci.dll" (
    echo [OK] apci.dll found
) else (
    echo [ERROR] apci.dll not found!
)

if exist "VolvoFlashWR.Launcher.exe" (
    echo [OK] VolvoFlashWR.Launcher.exe found
) else (
    echo [ERROR] VolvoFlashWR.Launcher.exe not found!
)

echo.
echo Library verification completed!
echo.
pause
