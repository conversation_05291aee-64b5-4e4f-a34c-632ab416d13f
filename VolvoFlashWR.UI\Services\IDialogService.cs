using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace VolvoFlashWR.UI.Services
{
    /// <summary>
    /// Interface for dialog service to show various dialogs to the user
    /// </summary>
    public interface IDialogService
    {
        /// <summary>
        /// Shows an information message to the user
        /// </summary>
        /// <param name="title">The title of the dialog</param>
        /// <param name="message">The message to display</param>
        void ShowInformation(string title, string message);

        /// <summary>
        /// Shows an error message to the user
        /// </summary>
        /// <param name="title">The title of the dialog</param>
        /// <param name="message">The error message to display</param>
        void ShowError(string title, string message);

        /// <summary>
        /// Shows a warning message to the user
        /// </summary>
        /// <param name="title">The title of the dialog</param>
        /// <param name="message">The warning message to display</param>
        void ShowWarning(string title, string message);

        /// <summary>
        /// Shows a confirmation dialog to the user
        /// </summary>
        /// <param name="title">The title of the dialog</param>
        /// <param name="message">The message to display</param>
        /// <returns>True if the user confirmed, false otherwise</returns>
        bool ShowConfirmation(string title, string message);

        /// <summary>
        /// Shows an input dialog to the user
        /// </summary>
        /// <param name="title">The title of the dialog</param>
        /// <param name="message">The message to display</param>
        /// <param name="defaultValue">The default value for the input</param>
        /// <returns>The input value from the user, or null if canceled</returns>
        string ShowInputDialog(string title, string message, string defaultValue = "");

        /// <summary>
        /// Shows a file open dialog to the user
        /// </summary>
        /// <param name="title">The title of the dialog</param>
        /// <param name="filter">The file filter</param>
        /// <param name="defaultExt">The default extension</param>
        /// <returns>The selected file path, or null if canceled</returns>
        string ShowOpenFileDialog(string title, string filter, string defaultExt = "");

        /// <summary>
        /// Shows a file save dialog to the user
        /// </summary>
        /// <param name="title">The title of the dialog</param>
        /// <param name="filter">The file filter</param>
        /// <param name="defaultExt">The default extension</param>
        /// <param name="defaultFileName">The default file name</param>
        /// <returns>The selected file path, or null if canceled</returns>
        string ShowSaveFileDialog(string title, string filter, string defaultExt = "", string defaultFileName = "");

        /// <summary>
        /// Shows a folder browser dialog to the user
        /// </summary>
        /// <param name="title">The title of the dialog</param>
        /// <param name="initialFolder">The initial folder to display</param>
        /// <returns>The selected folder path, or null if canceled</returns>
        string ShowFolderBrowserDialog(string title, string initialFolder = "");

        /// <summary>
        /// Shows a progress dialog to the user
        /// </summary>
        /// <param name="title">The title of the dialog</param>
        /// <param name="message">The message to display</param>
        /// <param name="operation">The operation to perform</param>
        /// <param name="canCancel">Whether the operation can be canceled</param>
        /// <returns>True if the operation completed successfully, false if it was canceled</returns>
        Task<bool> ShowProgressDialog(string title, string message, Func<IProgress<double>, Task> operation, bool canCancel = true);
    }
}
